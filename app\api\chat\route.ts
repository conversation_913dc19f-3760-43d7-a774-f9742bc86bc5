import { NextRequest, NextResponse } from 'next/server';
import { createAIClient, Message } from '@/lib/ai-client';
import { ModelProvider } from '@/lib/models';
import { createDefaultPrompt, createPresentationPrompt, createDataVisualizationPrompt } from '@/lib/prompt-modules';
import { AIConfigManager } from '@/lib/ai-store';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { messages, model } = body;
    // 原始消息，可能包含 'task' 角色
    const rawMessages = messages as Array<{ role: string; content: string }>;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Invalid messages format' },
        { status: 400 }
      );
    }

    // 在没有 system 消息时，前置系统提示
    if (!rawMessages.some(msg => msg.role === 'system')) {
      // 根据请求中的任务类型选择适当的提示词
      let systemPrompt = createPresentationPrompt();
      console.log('使用系统提示:', systemPrompt);

      // 检查请求中是否包含任务类型信息
      // const taskType = request.headers.get('x-task-type');
      // if (taskType) {
      //   switch (taskType) {
      //     case 'presentation':
      //       systemPrompt = createPresentationPrompt();
      //       break;
      //     case 'data-visualization':
      //       systemPrompt = createDataVisualizationPrompt();
      //       break;
      //     // 可以根据需要添加更多任务类型
      //   }
      // }

      rawMessages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    // 为API准备消息：将自定义 'task' 角色映射为 'user'
    const apiMessages: Message[] = rawMessages.map(msg => ({
      role: msg.role === 'task' ? 'user' : (msg.role as 'user' | 'assistant' | 'system'),
      content: msg.content,
    }));

    // 过滤掉空内容消息，避免 API 报错
    const filteredMessages = apiMessages.filter(m => m.content?.trim());

    // 获取API密钥和基础URL
    const providerCookie = request.cookies.get('ai_provider');
    const modelCookie = request.cookies.get('ai_model');

    // 根据模型名称确定提供商
    let provider: ModelProvider;

    // 添加调试日志
    console.log('模型名称:', model);

    // 使用更严格的条件判断
    if (model.startsWith('gpt-') || model === 'gpt-4o') {
      provider = 'openai';
    } else if (model.startsWith('grok-')) {
      provider = 'xai';
    } else if (model === 'deepseek-coder' || model.startsWith('deepseek-')) {
      provider = 'deepseek';
    } else {
      // 默认提供商
      provider = 'openai';
    }

    // 添加调试日志
    console.log('识别的提供商:', provider);

    // 获取特定提供商的API密钥和基础URL
    const apiKeyCookie = request.cookies.get(`ai_api_key_${provider}`);
    const baseUrlCookie = request.cookies.get(`ai_base_url_${provider}`);
    const apiKey = apiKeyCookie?.value || '';
    const baseUrl = baseUrlCookie?.value || '';

    // 添加调试日志
    console.log('API调用配置:', {
      model,
      provider,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      hasBaseUrl: !!baseUrl,
      baseUrlLength: baseUrl?.length || 0,
      cookieName: `ai_api_key_${provider}`
    });

    // 创建API密钥和基础URL对象
    const apiKeys: Partial<Record<ModelProvider, string>> = {};
    apiKeys[provider as ModelProvider] = apiKey;

    const baseUrls: Partial<Record<ModelProvider, string>> = {};
    if (baseUrl) {
      baseUrls[provider as ModelProvider] = baseUrl;
    }

    // 获取模型配置
    const cookieStore = cookies();
    const configManager = new AIConfigManager(cookieStore);
    const { customModels } = await configManager.getConfig();

    // 创建AI客户端
    const aiClient = createAIClient({
      apiKeys: apiKeys as Record<ModelProvider, string>,
      baseUrls: baseUrls as Record<ModelProvider, string>,
      customModels: customModels
    });

    // 调用AI客户端
    const response = await aiClient.chatCompletion({
      model,
      messages: filteredMessages,
    });

    return NextResponse.json({
      message: {
        role: 'assistant',
        content: response.content,
        timestamp: Date.now(),
      },
      model: response.model,
    });
  } catch (error) {
    console.error('Error in chat API:', error);

    // 返回备用响应
    return NextResponse.json({
      message: {
        role: 'assistant',
        content: '抱歉，我暂时无法处理您的请求。请稍后再试或检查您的API配置。',
        timestamp: Date.now(),
      },
      model: 'fallback',
    });
  }
}
